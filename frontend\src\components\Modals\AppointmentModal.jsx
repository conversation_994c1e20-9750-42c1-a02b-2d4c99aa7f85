import { useState, useEffect, useCallback, useRef } from 'react'
import { FiX, FiCalendar, FiUser, FiPhone, FiMail, FiClock, FiSearch } from 'react-icons/fi'
import { adminService, serviceService } from '../../services'
import PhoneInput from '../PhoneInput'
import { useToast } from '../../contexts/ToastContext'

const AppointmentModal = ({
  isOpen,
  onClose,
  onSave,
  editingItem,
  modalType,
  branding,
  currentUser = null,
  isUserMode = false
}) => {
  const { showError } = useToast()
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    customerEmail: '',
    customerPhone: '',
    userId: '',
    service: '',
    date: '',
    time: '',
    notes: '',
    status: 'scheduled'
  })

  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Customer search states
  const [customerSearchTerm, setCustomerSearchTerm] = useState('')
  const [customers, setCustomers] = useState([])
  const [isSearching, setIsSearching] = useState(false)
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState(null)
  const searchTimeoutRef = useRef(null)
  const dropdownRef = useRef(null)

  // Services state
  const [services, setServices] = useState([])
  const [isLoadingServices, setIsLoadingServices] = useState(false)

  // Helper function to format duration from minutes to readable format
  const formatDuration = (minutes) => {
    if (!minutes) return 'Duration not set'
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60

    if (hours === 0) {
      return `${mins} min${mins !== 1 ? 's' : ''}`
    } else if (mins === 0) {
      return `${hours} hour${hours !== 1 ? 's' : ''}`
    } else {
      return `${hours}h ${mins}m`
    }
  }

  // Helper function to format time from 24-hour to 12-hour format
  const formatTime = (time24) => {
    if (!time24) return 'Time not set'

    // Handle both HH:MM and HH:MM:SS formats
    const timeParts = time24.split(':')
    if (timeParts.length < 2) return time24

    let hours = parseInt(timeParts[0])
    const minutes = timeParts[1]
    const ampm = hours >= 12 ? 'PM' : 'AM'

    // Convert to 12-hour format
    hours = hours % 12
    hours = hours ? hours : 12 // 0 should be 12

    return `${hours}:${minutes} ${ampm}`
  }

  // Debounced customer search
  const searchCustomers = useCallback(async (searchTerm) => {
    // Only allow customer search in admin mode
    if (isUserMode) {
      setCustomers([])
      setIsSearching(false)
      return
    }

    if (!searchTerm.trim()) {
      setCustomers([])
      setIsSearching(false)
      return
    }

    setIsSearching(true)
    try {
      const response = await adminService.getCustomers({
        search: searchTerm,
        limit: 10
      })

      if (response.success) {
        setCustomers(response.data.customers || [])
      }
    } catch (error) {
      console.error('Error searching customers:', error)
      showError(`Failed to search customers: ${error.response?.data?.message || error.message || 'Unknown error'}`)
      setCustomers([])
    } finally {
      setIsSearching(false)
    }
  }, [isUserMode])

  // Debounced search effect
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }

    searchTimeoutRef.current = setTimeout(() => {
      searchCustomers(customerSearchTerm)
    }, 300)

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current)
      }
    }
  }, [customerSearchTerm, searchCustomers])

  // Click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowCustomerDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Reset form when modal opens/closes or editing item changes
  useEffect(() => {
    if (isOpen) {
      if (editingItem && modalType === 'edit') {
        // Handle both old and new data structures
        const customerName = editingItem.customerName || editingItem.customerInfo?.name || ''
        const customerEmail = editingItem.customerEmail || editingItem.customerInfo?.email || ''
        const customerPhone = editingItem.customerPhone || editingItem.customerInfo?.phone || ''
        const serviceId = editingItem.service?._id || editingItem.serviceId || editingItem.service || ''
        const userId = editingItem.userId || editingItem.user?._id || ''
        const appointmentDate = editingItem.date ? editingItem.date.split('T')[0] : ''
        const notes = editingItem.notes || editingItem.message || ''

        // Split customerName for firstName/lastName
        const nameParts = customerName.split(' ')

        setFormData({
          firstName: nameParts[0] || '',
          lastName: nameParts.slice(1).join(' ') || '',
          customerEmail: customerEmail,
          customerPhone: customerPhone,
          userId: userId,
          service: serviceId,
          date: appointmentDate,
          time: editingItem.time || '',
          notes: notes,
          status: editingItem.status || 'scheduled'
        })

        // Set selected customer for editing
        if (userId) {
          setSelectedCustomer({
            _id: userId,
            name: customerName || `${nameParts[0] || ''} ${nameParts.slice(1).join(' ') || ''}`.trim(),
            email: customerEmail,
            phone: customerPhone
          })
          setCustomerSearchTerm(customerName)
        }
      } else {
        // If in user mode, automatically set current user data
        if (isUserMode && currentUser) {
          setFormData({
            firstName: currentUser.firstName || '',
            lastName: currentUser.lastName || '',
            customerEmail: currentUser.email || '',
            customerPhone: currentUser.phone || '',
            userId: currentUser._id || '',
            service: '',
            date: '',
            time: '',
            notes: '',
            status: 'scheduled'
          })
          setSelectedCustomer({
            _id: currentUser._id,
            firstName: currentUser.firstName,
            lastName: currentUser.lastName,
            name: `${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim() || currentUser.name,
            email: currentUser.email,
            phone: currentUser.phone
          })
          setCustomerSearchTerm(`${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim() || currentUser.name || currentUser.email)
        } else {
          setFormData({
            firstName: '',
            lastName: '',
            customerEmail: '',
            customerPhone: '',
            userId: '',
            service: '',
            date: '',
            time: '',
            notes: '',
            status: 'scheduled'
          })
          setSelectedCustomer(null)
          setCustomerSearchTerm('')
        }
      }
      setErrors({})
      setShowCustomerDropdown(false)
    }
  }, [isOpen, editingItem, modalType, isUserMode, currentUser])

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // Handle customer selection
  const handleCustomerSelect = (customer) => {
    setSelectedCustomer(customer)
    setCustomerSearchTerm(`${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.name || customer.email)
    setFormData(prev => ({
      ...prev,
      userId: customer._id,
      firstName: customer.firstName || customer.name?.split(' ')[0] || '',
      lastName: customer.lastName || customer.name?.split(' ').slice(1).join(' ') || '',
      customerEmail: customer.email || '',
      customerPhone: customer.phone || ''
    }))
    setShowCustomerDropdown(false)

    // Clear userId error if it exists
    if (errors.userId) {
      setErrors(prev => ({
        ...prev,
        userId: ''
      }))
    }
  }

  // Handle customer search input change
  const handleCustomerSearchChange = (value) => {
    setCustomerSearchTerm(value)
    setShowCustomerDropdown(true)

    // If user clears the search, clear the selected customer
    if (!value.trim()) {
      setSelectedCustomer(null)
      setFormData(prev => ({
        ...prev,
        userId: '',
        firstName: '',
        lastName: '',
        customerEmail: '',
        customerPhone: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required'
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required'
    }
    if (!formData.userId.trim()) {
      newErrors.userId = 'Customer selection is required'
    }
    if (!formData.customerEmail.trim()) {
      newErrors.customerEmail = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.customerEmail)) {
      newErrors.customerEmail = 'Please enter a valid email'
    }
    if (!isUserMode && !formData.customerPhone.trim()) {
      newErrors.customerPhone = 'Phone number is required'
    }
    if (!formData.service.trim()) {
      newErrors.service = 'Service is required'
    }
    if (!formData.date) {
      newErrors.date = 'Date is required'
    }
    if (!formData.time) {
      newErrors.time = 'Time is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Helper function to convert 12-hour time to 24-hour format
  const convertTo24Hour = (time12h) => {
    if (!time12h) return ''

    const [time, modifier] = time12h.split(' ')
    let [hours, minutes] = time.split(':')

    if (hours === '12') {
      hours = '00'
    }

    if (modifier === 'PM') {
      hours = parseInt(hours, 10) + 12
    }

    return `${hours.toString().padStart(2, '0')}:${minutes}`
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      // Convert time to 24-hour format before saving
      const appointmentData = {
        ...formData,
        time: convertTo24Hour(formData.time)
      }
      await onSave(appointmentData)
      onClose()
    } catch (error) {
      console.error('Error saving appointment:', error)
      showError(`Failed to save appointment: ${error.response?.data?.message || error.response?.data?.error || error.message || 'Unknown error'}`)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Load services when modal opens
  useEffect(() => {
    if (isOpen) {
      const loadServices = async () => {
        try {
          setIsLoadingServices(true)
          const response = await serviceService.getServices()
          if (response.success) {
            setServices(response.data)
          }
        } catch (error) {
          console.error('Error loading services:', error)
          showError(`Failed to load services: ${error.response?.data?.message || error.message || 'Unknown error'}`)
        } finally {
          setIsLoadingServices(false)
        }
      }
      loadServices()
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[70] p-2 sm:p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200">
          <h2 className="text-lg sm:text-2xl font-bold text-gray-900 truncate">
            {modalType === 'view' ? 'Appointment Details' : modalType === 'edit' ? 'Edit Appointment' : 'Add New Appointment'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200 flex-shrink-0"
          >
            <FiX className="w-5 h-5 sm:w-6 sm:h-6 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        {modalType === 'view' ? (
          /* View Mode - Read-only display */
          <div className="p-6 space-y-6">
            {/* Customer Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <FiUser className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
                Customer Information
              </h3>

              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Customer Name</label>
                    <div className="text-gray-900 font-medium">
                      {editingItem?.customerInfo?.name || editingItem?.user?.name || 'N/A'}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Email</label>
                    <div className="text-gray-900">
                      {editingItem?.customerInfo?.email || editingItem?.user?.email || 'N/A'}
                    </div>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Phone</label>
                  <div className="text-gray-900">
                    {editingItem?.customerInfo?.phone || editingItem?.user?.phone || 'N/A'}
                  </div>
                </div>
              </div>
            </div>

            {/* Service Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <FiCalendar className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
                Service Details
              </h3>

              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Service</label>
                    <div className="text-gray-900 font-medium">
                      {editingItem?.service?.name || editingItem?.service || 'N/A'}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Duration</label>
                    <div className="text-gray-900">
                      {formatDuration(editingItem?.service?.duration)}
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Price</label>
                    <div className="text-gray-900 font-medium">
                      {editingItem?.service?.price ? `$${editingItem.service.price}` : 'N/A'}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Status</label>
                    <div>
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        editingItem?.status === 'confirmed'
                          ? 'bg-green-100 text-green-800'
                          : editingItem?.status === 'completed'
                          ? 'bg-blue-100 text-blue-800'
                          : editingItem?.status === 'cancelled'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {editingItem?.status || 'Pending'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Appointment Details */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <FiClock className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
                Appointment Details
              </h3>

              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Date</label>
                    <div className="text-gray-900 font-medium">
                      {editingItem?.date ? new Date(editingItem.date).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      }) : 'N/A'}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Time</label>
                    <div className="text-gray-900 font-medium">
                      {formatTime(editingItem?.time)}
                    </div>
                  </div>
                </div>
                {editingItem?.message && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Notes</label>
                    <div className="text-gray-900 bg-white p-3 rounded border">
                      {editingItem.message}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Actions for View Mode */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                Close
              </button>
              <button
                type="button"
                onClick={() => {
                  onClose()
                  // Trigger edit mode through parent component
                  setTimeout(() => {
                    if (window.switchToEditMode) {
                      window.switchToEditMode(editingItem)
                    }
                  }, 100)
                }}
                className="px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200"
              >
                Edit Appointment
              </button>
            </div>
          </div>
        ) : (
          /* Edit/Add Mode - Form */
          <form onSubmit={handleSubmit} className="p-4 sm:p-6 space-y-4 sm:space-y-6">
          {/* Customer Information */}
          <div className="space-y-3 sm:space-y-4">
            <h3 className="text-base sm:text-lg font-semibold text-gray-900 flex items-center">
              <FiUser className="w-4 h-4 sm:w-5 sm:h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Customer Information
            </h3>
            
            {/* Customer Details - Only show in admin mode */}
            {!isUserMode && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        First Name *
                      </label>
                      <input
                        type="text"
                        value={formData.firstName}
                        onChange={(e) => handleInputChange('firstName', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                          errors.firstName ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Enter first name"
                      />
                      {errors.firstName && (
                        <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        value={formData.lastName}
                        onChange={(e) => handleInputChange('lastName', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                          errors.lastName ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Enter last name"
                      />
                      {errors.lastName && (
                        <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number {isUserMode ? '(Optional)' : '*'}
                    </label>
                    <PhoneInput
                      value={formData.customerPhone}
                      onChange={(value) => handleInputChange('customerPhone', value)}
                      placeholder="Enter phone number"
                      error={errors.customerPhone}
                      className={errors.customerPhone ? 'border-red-500' : 'border-gray-300'}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    value={formData.customerEmail}
                    onChange={(e) => handleInputChange('customerEmail', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.customerEmail ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter email address"
                  />
                  {errors.customerEmail && (
                    <p className="text-red-500 text-sm mt-1">{errors.customerEmail}</p>
                  )}
                </div>
              </>
            )}

            {/* Customer Selection - Show different UI based on mode */}
            {isUserMode ? (
              /* User Mode - Show current user info */
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Booking Appointment For
                </label>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <FiUser className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">
                      {`${currentUser?.firstName || ''} ${currentUser?.lastName || ''}`.trim() || currentUser?.name || 'Current User'}
                    </div>
                    <div className="text-sm text-gray-500">
                      {currentUser?.email} {currentUser?.phone && `• ${currentUser.phone}`}
                    </div>
                  </div>
                </div>
                <p className="text-sm text-blue-600 mt-2">
                  This appointment will be created for your account
                </p>
              </div>
            ) : (
              /* Admin Mode - Show customer search */
              <div className="relative" ref={dropdownRef}>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Search Customer *
                </label>
                <div className="relative">
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    value={customerSearchTerm}
                    onChange={(e) => handleCustomerSearchChange(e.target.value)}
                    onFocus={() => setShowCustomerDropdown(true)}
                    className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.userId ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Search by name, email, or phone..."
                  />
                  {isSearching && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    </div>
                  )}
                </div>

                {/* Customer dropdown */}
                {showCustomerDropdown && customerSearchTerm && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {customers.length > 0 ? (
                      customers.map((customer) => (
                        <button
                          key={customer._id}
                          type="button"
                          onClick={() => handleCustomerSelect(customer)}
                          className="w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0"
                        >
                          <div className="font-medium text-gray-900">
                            {`${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.name || 'No Name'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {customer.email} {customer.phone && `• ${customer.phone}`}
                          </div>
                        </button>
                      ))
                    ) : !isSearching && customerSearchTerm ? (
                      <div className="px-3 py-2 text-gray-500 text-sm">
                        No customers found. You can still create the appointment with the entered details.
                      </div>
                    ) : null}
                  </div>
                )}

                {errors.userId && (
                  <p className="text-red-500 text-sm mt-1">{errors.userId}</p>
                )}
                <p className="text-sm text-gray-500 mt-1">
                  Start typing to search for existing customers or enter new customer details below
                </p>
              </div>
            )}
          </div>

          {/* Appointment Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <FiCalendar className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Appointment Details
            </h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service *
              </label>
              <select
                value={formData.service}
                onChange={(e) => handleInputChange('service', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.service ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">
                  {isLoadingServices ? 'Loading services...' : 'Select a service'}
                </option>
                {services.map(service => (
                  <option key={service._id} value={service._id}>
                    {service.name} - ${service.price} ({service.duration}min)
                  </option>
                ))}
              </select>
              {errors.service && (
                <p className="text-red-500 text-sm mt-1">{errors.service}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date *
                </label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange('date', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.date ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.date && (
                  <p className="text-red-500 text-sm mt-1">{errors.date}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Time *
                </label>
                <input
                  type="time"
                  value={formData.time}
                  onChange={(e) => handleInputChange('time', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.time ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.time && (
                  <p className="text-red-500 text-sm mt-1">{errors.time}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="scheduled">Scheduled</option>
                <option value="confirmed">Confirmed</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notes
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="Additional notes or special requests..."
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Saving...' : (modalType === 'edit' ? 'Update Appointment' : 'Create Appointment')}
            </button>
          </div>
        </form>
        )}
      </div>
    </div>
  )
}

export default AppointmentModal
