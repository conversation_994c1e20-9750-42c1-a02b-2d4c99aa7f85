const { MongoClient } = require('mongodb');
const fs = require('fs');
const path = require('path');

// Database connection strings
const SOURCE_URI = 'mongodb+srv://inspiredolajosh:<EMAIL>/MicrolocsHq?retryWrites=true&w=majority';
const TARGET_URI = 'mongodb+srv://dammyspicy4:<EMAIL>/MicrolocsHq?retryWrites=true&w=majority';

// Database names
const SOURCE_DB = 'MicrolocsHq';
const TARGET_DB = 'MicrolocsHq';

// Collections to migrate (add/remove as needed)
const COLLECTIONS_TO_MIGRATE = [
  'users',
  'services',
  'appointments',
  'products',
  'categories',
  'orders',
  'carts',
  'reviews',
  'paymentconfirmations',
  'branding',
  'media'
];

class DatabaseMigrator {
  constructor() {
    this.sourceClient = null;
    this.targetClient = null;
    this.migrationLog = [];
  }

  async connect() {
    console.log('🔗 Connecting to databases...');
    
    try {
      // Connect to source database
      this.sourceClient = new MongoClient(SOURCE_URI);
      await this.sourceClient.connect();
      console.log('✅ Connected to source database');

      // Connect to target database
      this.targetClient = new MongoClient(TARGET_URI);
      await this.targetClient.connect();
      console.log('✅ Connected to target database');

      // Test connections
      await this.sourceClient.db(SOURCE_DB).admin().ping();
      await this.targetClient.db(TARGET_DB).admin().ping();
      console.log('✅ Database connections verified');

    } catch (error) {
      console.error('❌ Connection failed:', error.message);
      throw error;
    }
  }

  async getCollections() {
    try {
      const sourceDb = this.sourceClient.db(SOURCE_DB);
      const collections = await sourceDb.listCollections().toArray();
      const collectionNames = collections.map(col => col.name);
      
      console.log('📋 Available collections in source database:');
      collectionNames.forEach(name => console.log(`   - ${name}`));
      
      return collectionNames;
    } catch (error) {
      console.error('❌ Failed to get collections:', error.message);
      throw error;
    }
  }

  async migrateCollection(collectionName) {
    try {
      console.log(`\n📦 Migrating collection: ${collectionName}`);
      
      const sourceDb = this.sourceClient.db(SOURCE_DB);
      const targetDb = this.targetClient.db(TARGET_DB);
      
      const sourceCollection = sourceDb.collection(collectionName);
      const targetCollection = targetDb.collection(collectionName);

      // Get document count
      const totalDocs = await sourceCollection.countDocuments();
      console.log(`   📊 Total documents: ${totalDocs}`);

      if (totalDocs === 0) {
        console.log(`   ⚠️  Collection ${collectionName} is empty, skipping...`);
        this.migrationLog.push({
          collection: collectionName,
          status: 'skipped',
          reason: 'empty',
          documents: 0
        });
        return;
      }

      // Check if target collection exists and has data
      const targetCount = await targetCollection.countDocuments();
      if (targetCount > 0) {
        console.log(`   ⚠️  Target collection ${collectionName} already has ${targetCount} documents`);
        const answer = await this.promptUser(`Do you want to clear the target collection ${collectionName} before migration? (y/n): `);
        if (answer.toLowerCase() === 'y') {
          await targetCollection.deleteMany({});
          console.log(`   🗑️  Cleared target collection ${collectionName}`);
        }
      }

      // Migrate documents in batches
      const batchSize = 1000;
      let migratedCount = 0;
      let cursor = sourceCollection.find({});

      while (await cursor.hasNext()) {
        const batch = [];
        
        // Collect batch
        for (let i = 0; i < batchSize && await cursor.hasNext(); i++) {
          const doc = await cursor.next();
          batch.push(doc);
        }

        if (batch.length > 0) {
          // Insert batch to target
          await targetCollection.insertMany(batch, { ordered: false });
          migratedCount += batch.length;
          
          const progress = ((migratedCount / totalDocs) * 100).toFixed(1);
          console.log(`   📈 Progress: ${migratedCount}/${totalDocs} (${progress}%)`);
        }
      }

      console.log(`   ✅ Successfully migrated ${migratedCount} documents`);
      this.migrationLog.push({
        collection: collectionName,
        status: 'success',
        documents: migratedCount
      });

    } catch (error) {
      console.error(`   ❌ Failed to migrate ${collectionName}:`, error.message);
      this.migrationLog.push({
        collection: collectionName,
        status: 'failed',
        error: error.message,
        documents: 0
      });
    }
  }

  async promptUser(question) {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question(question, (answer) => {
        rl.close();
        resolve(answer);
      });
    });
  }

  async createIndexes() {
    try {
      console.log('\n🔧 Creating indexes on target database...');
      const targetDb = this.targetClient.db(TARGET_DB);

      // User indexes
      await targetDb.collection('users').createIndex({ email: 1 }, { unique: true });
      console.log('   ✅ Created unique index on users.email');

      // Appointment indexes
      await targetDb.collection('appointments').createIndex({ user: 1 });
      await targetDb.collection('appointments').createIndex({ service: 1 });
      await targetDb.collection('appointments').createIndex({ date: 1 });
      await targetDb.collection('appointments').createIndex({ status: 1 });
      await targetDb.collection('appointments').createIndex({ date: 1, time: 1 });
      console.log('   ✅ Created indexes on appointments collection');

      // Product indexes
      await targetDb.collection('products').createIndex({ name: 1 });
      await targetDb.collection('products').createIndex({ category: 1 });
      await targetDb.collection('products').createIndex({ isActive: 1 });
      console.log('   ✅ Created indexes on products collection');

      // Cart indexes
      await targetDb.collection('carts').createIndex({ user: 1 });
      console.log('   ✅ Created indexes on carts collection');

      // Review indexes
      await targetDb.collection('reviews').createIndex({ product: 1 });
      await targetDb.collection('reviews').createIndex({ user: 1 });
      console.log('   ✅ Created indexes on reviews collection');

    } catch (error) {
      console.error('❌ Failed to create indexes:', error.message);
    }
  }

  async generateReport() {
    console.log('\n📊 Migration Report:');
    console.log('='.repeat(50));
    
    let totalSuccess = 0;
    let totalFailed = 0;
    let totalDocuments = 0;

    this.migrationLog.forEach(log => {
      const status = log.status === 'success' ? '✅' : 
                    log.status === 'failed' ? '❌' : '⚠️';
      console.log(`${status} ${log.collection}: ${log.documents} documents`);
      
      if (log.status === 'success') {
        totalSuccess++;
        totalDocuments += log.documents;
      } else if (log.status === 'failed') {
        totalFailed++;
        console.log(`   Error: ${log.error}`);
      }
    });

    console.log('='.repeat(50));
    console.log(`📈 Summary:`);
    console.log(`   ✅ Successful collections: ${totalSuccess}`);
    console.log(`   ❌ Failed collections: ${totalFailed}`);
    console.log(`   📄 Total documents migrated: ${totalDocuments}`);

    // Save report to file
    const reportPath = path.join(__dirname, 'migration-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      sourceUri: SOURCE_URI,
      targetUri: TARGET_URI.replace(/\/\/.*@/, '//***:***@'), // Hide credentials
      summary: {
        successfulCollections: totalSuccess,
        failedCollections: totalFailed,
        totalDocuments: totalDocuments
      },
      details: this.migrationLog
    }, null, 2));

    console.log(`📋 Detailed report saved to: ${reportPath}`);
  }

  async disconnect() {
    try {
      if (this.sourceClient) {
        await this.sourceClient.close();
        console.log('🔌 Disconnected from source database');
      }
      if (this.targetClient) {
        await this.targetClient.close();
        console.log('🔌 Disconnected from target database');
      }
    } catch (error) {
      console.error('❌ Error during disconnect:', error.message);
    }
  }

  async migrate() {
    try {
      console.log('🚀 Starting database migration...');
      console.log(`📍 Source: ${SOURCE_URI}`);
      console.log(`📍 Target: ${TARGET_URI.replace(/\/\/.*@/, '//***:***@')}`);
      
      await this.connect();
      
      // Get available collections
      const availableCollections = await this.getCollections();
      
      // Filter collections to migrate
      const collectionsToMigrate = COLLECTIONS_TO_MIGRATE.filter(col => 
        availableCollections.includes(col)
      );

      console.log(`\n📦 Collections to migrate: ${collectionsToMigrate.length}`);
      collectionsToMigrate.forEach(name => console.log(`   - ${name}`));

      // Confirm migration
      const confirm = await this.promptUser('\n❓ Do you want to proceed with the migration? (y/n): ');
      if (confirm.toLowerCase() !== 'y') {
        console.log('❌ Migration cancelled by user');
        return;
      }

      // Migrate each collection
      for (const collectionName of collectionsToMigrate) {
        await this.migrateCollection(collectionName);
      }

      // Create indexes
      await this.createIndexes();

      // Generate report
      await this.generateReport();

      console.log('\n🎉 Migration completed successfully!');

    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      throw error;
    } finally {
      await this.disconnect();
    }
  }
}

// Main execution
async function main() {
  const migrator = new DatabaseMigrator();
  
  try {
    await migrator.migrate();
  } catch (error) {
    console.error('💥 Migration process failed:', error.message);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n⚠️  Migration interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n⚠️  Migration terminated');
  process.exit(0);
});

// Run migration if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = DatabaseMigrator;
