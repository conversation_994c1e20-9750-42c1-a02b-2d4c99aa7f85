import { useState, useEffect } from 'react'
import { FiCalendar, FiClock, FiMapPin, FiUser, FiPhone, FiMail, FiPlus, FiEye, FiTrash2, FiStar, FiChevronLeft, FiChevronRight } from 'react-icons/fi'
import { useBranding } from '../../../contexts/BrandingContext'
import { useToast } from '../../../contexts/ToastContext'

const UserAppointments = ({
  appointments,
  sectionLoading,
  setShowAddModal,
  setModalType,
  setEditingItem,
  setViewingItem,
  setConfirmDialog,
  handleDeleteAppointment,
  onReviewService
}) => {
  const { branding } = useBranding()
  const { showSuccess, showError, showWarning } = useToast()
  const [filterStatus, setFilterStatus] = useState('all')

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 5

  // Helper functions for formatting
  const formatDuration = (minutes) => {
    if (!minutes || minutes === 0) return '0 mins'

    const days = Math.floor(minutes / (24 * 60))
    const hours = Math.floor((minutes % (24 * 60)) / 60)
    const mins = minutes % 60

    const parts = []

    if (days > 0) {
      parts.push(`${days} day${days > 1 ? 's' : ''}`)
    }
    if (hours > 0) {
      parts.push(`${hours} hour${hours > 1 ? 's' : ''}`)
    }
    if (mins > 0) {
      parts.push(`${mins} min${mins > 1 ? 's' : ''}`)
    }

    return parts.join(' ')
  }

  const formatTime = (timeString) => {
    if (!timeString) return 'Time not set'

    try {
      // Handle different time formats
      let time
      if (timeString.includes(':')) {
        // Format: "HH:MM" or "HH:MM:SS"
        const [hours, minutes] = timeString.split(':')
        time = new Date()
        time.setHours(parseInt(hours), parseInt(minutes), 0, 0)
      } else {
        // Try to parse as a full date/time string
        time = new Date(timeString)
      }

      // Format to 12-hour format
      return time.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch (error) {
      console.error('Error formatting time:', error)
      return timeString // Return original if formatting fails
    }
  }

  const formatFullDate = (dateString) => {
    if (!dateString) return 'Date not set'

    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch (error) {
      console.error('Error formatting date:', error)
      return dateString // Return original if formatting fails
    }
  }

  const filteredAppointments = (appointments || []).filter(appointment => {
    if (filterStatus === 'all') return true
    return appointment.status === filterStatus
  })

  // Pagination logic
  const totalPages = Math.ceil(filteredAppointments.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedAppointments = filteredAppointments.slice(startIndex, endIndex)

  // Reset to first page when filter changes
  useEffect(() => {
    setCurrentPage(1)
  }, [filterStatus])

  const handlePageChange = (page) => {
    setCurrentPage(page)
    // Scroll to top of appointments section
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-yellow-100 text-yellow-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">My Appointments</h2>
          <p className="text-gray-600 mt-1">View and manage your scheduled appointments</p>
        </div>
        <button
          onClick={() => {
            setShowAddModal(true)
            setModalType('add')
            setEditingItem(null)
          }}
          className="flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
        >
          <FiPlus className="w-5 h-5 mr-2" />
          Book Appointment
        </button>
      </div>

      {/* Filter Tabs */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <div className="flex flex-wrap gap-2">
          {[
            { id: 'all', label: 'All Appointments', count: (appointments || []).length },
            { id: 'pending', label: 'Pending', count: (appointments || []).filter(a => a.status === 'pending').length },
            { id: 'confirmed', label: 'Confirmed', count: (appointments || []).filter(a => a.status === 'confirmed').length },
            { id: 'completed', label: 'Completed', count: (appointments || []).filter(a => a.status === 'completed').length },
            { id: 'cancelled', label: 'Cancelled', count: (appointments || []).filter(a => a.status === 'cancelled').length }
          ].map((filter) => (
            <button
              key={filter.id}
              onClick={() => setFilterStatus(filter.id)}
              className={`px-4 py-2 rounded-lg transition-colors duration-200 ${
                filterStatus === filter.id
                  ? 'bg-blue-50 text-blue-700 border border-blue-200'
                  : 'bg-gray-50 text-gray-700 hover:bg-gray-100 border border-transparent'
              }`}
            >
              {filter.label} ({filter.count})
            </button>
          ))}
        </div>
      </div>

      {/* Appointments List */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        {sectionLoading.appointments ? (
          <div className="p-6">
            <div className="space-y-4">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl animate-pulse">
                  <div className="w-16 h-16 bg-gray-300 rounded-xl"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-32"></div>
                    <div className="h-3 bg-gray-300 rounded w-24"></div>
                    <div className="h-3 bg-gray-300 rounded w-40"></div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-16"></div>
                    <div className="h-6 bg-gray-300 rounded w-20"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : filteredAppointments.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {paginatedAppointments.map((appointment, index) => (
              <div key={index} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="w-16 h-16 rounded-xl flex items-center justify-center"
                         style={{ background: `linear-gradient(135deg, ${branding.colors.primary}20, ${branding.colors.secondary}20)` }}>
                      <FiCalendar className="w-8 h-8" style={{ color: branding.colors.secondary }} />
                    </div>
                    
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {appointment.service?.name || appointment.service || appointment.serviceType || 'Service Appointment'}
                      </h3>

                      {appointment.service?.category && (
                        <p className="text-sm text-blue-600 font-medium mb-3">
                          {appointment.service.category}
                        </p>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <FiCalendar className="w-4 h-4 mr-2 text-gray-400" />
                          <span>
                            {appointment.date ? formatFullDate(appointment.date) : 'Date not set'}
                          </span>
                        </div>

                        <div className="flex items-center">
                          <FiClock className="w-4 h-4 mr-2 text-gray-400" />
                          <span>{formatTime(appointment.time)}</span>
                        </div>

                        {appointment.service?.duration && (
                          <div className="flex items-center">
                            <FiClock className="w-4 h-4 mr-2 text-gray-400" />
                            <span>Duration: {formatDuration(appointment.service.duration)}</span>
                          </div>
                        )}

                        {appointment.service?.price && (
                          <div className="flex items-center">
                            <span className="text-green-600 font-semibold">${appointment.service.price}</span>
                          </div>
                        )}

                        {appointment.customerInfo?.name && (
                          <div className="flex items-center">
                            <FiUser className="w-4 h-4 mr-2 text-gray-400" />
                            <span>{appointment.customerInfo.name}</span>
                          </div>
                        )}

                        {appointment.customerInfo?.phone && (
                          <div className="flex items-center">
                            <FiPhone className="w-4 h-4 mr-2 text-gray-400" />
                            <span>{appointment.customerInfo.phone}</span>
                          </div>
                        )}
                      </div>

                      {appointment.message && (
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm text-gray-700">
                            <strong>Message:</strong> {appointment.message}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex flex-col items-end space-y-3">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                      {appointment.status || 'Pending'}
                    </span>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          setViewingItem(appointment)
                          setEditingItem(appointment)
                          setModalType('view')
                        }}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        title="View Details"
                      >
                        <FiEye className="w-4 h-4" />
                      </button>
                      


                      {appointment.status === 'completed' && onReviewService && (
                        <button
                          onClick={() => onReviewService(appointment)}
                          className="p-2 text-yellow-600 hover:bg-yellow-50 rounded-lg transition-colors duration-200 cursor-pointer"
                          title="Write Review"
                        >
                          <FiStar className="w-4 h-4" />
                        </button>
                      )}
                      
                      <button
                        onClick={() => {
                          setConfirmDialog({
                            title: 'Cancel Appointment',
                            message: `Are you sure you want to cancel your ${appointment.service?.name || appointment.service || 'appointment'}?`,
                            onConfirm: () => handleDeleteAppointment(appointment._id || appointment.id || index)
                          })
                        }}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        title="Cancel"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <FiCalendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {filterStatus === 'all' ? 'No appointments found' : `No ${filterStatus} appointments`}
            </h3>
            <p className="text-gray-600 mb-6">
              {filterStatus === 'all' 
                ? 'Book your first appointment to get started.'
                : `You don't have any ${filterStatus} appointments.`
              }
            </p>
            {filterStatus === 'all' && (
              <button
                onClick={() => {
                  setShowAddModal(true)
                  setModalType('add')
                  setEditingItem(null)
                }}
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02] font-medium"
              >
                <FiCalendar className="w-5 h-5 mr-2" />
                Book Your First Appointment
              </button>
            )}
          </div>
        )}
      </div>

      {/* Pagination */}
      {filteredAppointments.length > itemsPerPage && (
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {startIndex + 1} to {Math.min(endIndex, filteredAppointments.length)} of {filteredAppointments.length} appointments
            </div>

            <div className="flex items-center space-x-2">
              {/* Previous Button */}
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                  currentPage === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-100 cursor-pointer'
                }`}
              >
                <FiChevronLeft className="w-4 h-4 mr-1" />
                Previous
              </button>

              {/* Page Numbers */}
              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 cursor-pointer ${
                      currentPage === page
                        ? 'text-white shadow-lg'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                    style={{
                      backgroundColor: currentPage === page ? branding.colors.secondary : 'transparent'
                    }}
                  >
                    {page}
                  </button>
                ))}
              </div>

              {/* Next Button */}
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                  currentPage === totalPages
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-100 cursor-pointer'
                }`}
              >
                Next
                <FiChevronRight className="w-4 h-4 ml-1" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Appointment Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <FiCalendar className="w-5 h-5 text-blue-600 mt-0.5" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Appointment Tips</h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Book appointments at least 24 hours in advance</li>
                <li>You can reschedule or cancel up to 2 hours before your appointment</li>
                <li>Arrive 10 minutes early for your appointment</li>
                <li>Contact us if you need to make any special arrangements</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserAppointments