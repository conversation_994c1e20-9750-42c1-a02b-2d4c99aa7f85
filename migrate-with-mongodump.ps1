# MongoDB Migration using mongodump/mongorestore
Write-Host "🚀 MongoDB Migration using mongodump/mongorestore" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green
Write-Host ""

# Database connection strings
$SOURCE_URI = "mongodb+srv://inspiredolajosh:<EMAIL>/MicrolocsHq"
$TARGET_URI = "mongodb+srv://dammyspicy4:<EMAIL>/MicrolocsHq"

# Backup directory
$BACKUP_DIR = "mongodb-backup"

Write-Host "📋 This script will:" -ForegroundColor Cyan
Write-Host "   1. Export data from source database using mongodump" -ForegroundColor White
Write-Host "   2. Import data to target database using mongorestore" -ForegroundColor White
Write-Host ""

# Create backup directory
if (!(Test-Path $BACKUP_DIR)) {
    New-Item -ItemType Directory -Path $BACKUP_DIR | Out-Null
    Write-Host "📁 Created backup directory: $BACKUP_DIR" -ForegroundColor Yellow
}

Write-Host "📦 Step 1: Exporting data from source database..." -ForegroundColor Cyan
Write-Host "Source: $SOURCE_URI" -ForegroundColor Gray
Write-Host ""

# Check if mongodump is available
try {
    $null = Get-Command mongodump -ErrorAction Stop
    Write-Host "✅ MongoDB tools found" -ForegroundColor Green
} catch {
    Write-Host "❌ MongoDB tools not found. Please install MongoDB Database Tools:" -ForegroundColor Red
    Write-Host "   Download from: https://www.mongodb.com/try/download/database-tools" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Export using mongodump
Write-Host "🔄 Running mongodump..." -ForegroundColor Yellow
try {
    & mongodump --uri="$SOURCE_URI" --out=$BACKUP_DIR
    
    if ($LASTEXITCODE -ne 0) {
        throw "mongodump failed with exit code $LASTEXITCODE"
    }
    
    Write-Host "✅ Export completed successfully!" -ForegroundColor Green
    Write-Host "📁 Data exported to: $BACKUP_DIR" -ForegroundColor Yellow
} catch {
    Write-Host "❌ Export failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please ensure:" -ForegroundColor Yellow
    Write-Host "   1. Source database is accessible" -ForegroundColor White
    Write-Host "   2. Network connectivity is working" -ForegroundColor White
    Write-Host "   3. Credentials are correct" -ForegroundColor White
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "📥 Step 2: Importing data to target database..." -ForegroundColor Cyan
Write-Host "Target: $TARGET_URI" -ForegroundColor Gray
Write-Host ""

# Confirm before importing
$confirm = Read-Host "Do you want to proceed with importing to the target database? This will overwrite existing data. (y/N)"
if ($confirm -ne "y" -and $confirm -ne "Y") {
    Write-Host "❌ Import cancelled by user" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 0
}

# Import using mongorestore
Write-Host "🔄 Running mongorestore..." -ForegroundColor Yellow
try {
    & mongorestore --uri="$TARGET_URI" --drop "$BACKUP_DIR/MicrolocsHq"
    
    if ($LASTEXITCODE -ne 0) {
        throw "mongorestore failed with exit code $LASTEXITCODE"
    }
    
    Write-Host "✅ Import completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Import failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please ensure:" -ForegroundColor Yellow
    Write-Host "   1. Target database is accessible" -ForegroundColor White
    Write-Host "   2. You have write permissions" -ForegroundColor White
    Write-Host "   3. Network connectivity is working" -ForegroundColor White
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "🎉 Migration completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Summary:" -ForegroundColor Cyan
Write-Host "   ✅ Data exported from source database" -ForegroundColor Green
Write-Host "   ✅ Data imported to target database" -ForegroundColor Green
Write-Host "   📁 Backup files saved in: $BACKUP_DIR" -ForegroundColor Yellow
Write-Host ""
Write-Host "🔍 You can verify the migration by checking the target database" -ForegroundColor Cyan

Read-Host "Press Enter to exit"
