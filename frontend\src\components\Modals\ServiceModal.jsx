import { useState, useEffect } from 'react'
import { FiX, FiSave, FiSettings, FiPlus, FiUpload, FiTrash2, FiImage, FiCamera } from 'react-icons/fi'
import categoryService from '../../services/categoryService'
import { serviceService } from '../../services'
import { useToast } from '../../contexts/ToastContext'
import MediaLibraryModal from './MediaLibraryModal'
import { useMediaLibrary } from '../../hooks/useMediaLibrary'
import { useDeviceImageUpload } from '../../hooks/useDeviceImageUpload.jsx'
import { isMobileDevice } from '../../utils/constants'

const ServiceModal = ({ isOpen, onClose, onSave, service, viewOnly = false, branding }) => {
  const { showSuccess, showError } = useToast()
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    duration: '',
    category: '',
    isActive: true
  })
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState({})
  const [categories, setCategories] = useState([])
  const [loadingCategories, setLoadingCategories] = useState(true)
  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false)
  const [newCategoryName, setNewCategoryName] = useState('')

  // Image upload state
  const [serviceImages, setServiceImages] = useState([])
  const [uploadingImage, setUploadingImage] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState([])

  // Media library hook
  const { mediaLibraryProps, openMediaLibrary } = useMediaLibrary()

  // Device-specific image upload hook
  const deviceImageUpload = useDeviceImageUpload({
    onUpload: async (files) => {
      if (!service?._id) {
        showError('Please save the service first before uploading images')
        return
      }

      setUploadingImage(true)
      try {
        // Delete existing images first (for mobile replacement behavior)
        if (serviceImages.length > 0 && isMobileDevice()) {
          try {
            await Promise.all(
              serviceImages.map(imageUrl =>
                serviceService.deleteServiceImage(service._id, imageUrl)
              )
            )
          } catch (deleteError) {
            console.warn('Some images could not be deleted:', deleteError)
          }
        }

        const uploadPromises = files.map(file =>
          serviceService.uploadServiceImage(service._id, file)
        )

        const results = await Promise.all(uploadPromises)
        const newImageUrls = results.map(result => result.data.imageUrl)

        // Replace images on mobile, add to existing on desktop
        if (isMobileDevice()) {
          setServiceImages(newImageUrls) // Replace all images
          showSuccess(`Service images updated successfully!`)
        } else {
          setServiceImages(prev => [...prev, ...newImageUrls]) // Add to existing
          showSuccess(`${files.length} image(s) uploaded successfully!`)
        }
      } catch (error) {
        console.error('Error uploading images:', error)
        showError('Failed to upload images')
      } finally {
        setUploadingImage(false)
      }
    },
    onError: (error) => {
      showError(error)
    },
    multiple: true,
    maxFileSize: 10 * 1024 * 1024, // 10MB for service images
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  })

  // Fetch complete service data when editing
  useEffect(() => {
    const fetchServiceData = async () => {
      if (service && service._id && isOpen) {
        try {
          // Fetch complete service data including images
          const response = await serviceService.getService(service._id)
          if (response.success) {
            const fullServiceData = response.data

            console.log('Fetched service data:', fullServiceData)

            setFormData({
              name: fullServiceData.name || '',
              description: fullServiceData.description || '',
              price: fullServiceData.price || '',
              duration: fullServiceData.duration || '',
              category: fullServiceData.category || '',
              isActive: fullServiceData.isActive !== undefined ? fullServiceData.isActive : true
            })

            // Load service images from the fetched data
            if (fullServiceData.images && fullServiceData.images.length > 0) {
              console.log('Setting service images from images array:', fullServiceData.images)
              setServiceImages(fullServiceData.images)
            } else if (fullServiceData.image) {
              console.log('Setting service images from single image:', fullServiceData.image)
              setServiceImages([fullServiceData.image])
            } else {
              console.log('No images found for service')
              setServiceImages([])
            }
          }
        } catch (error) {
          console.error('Error fetching service data:', error)
          showError('Failed to load service data')

          // Fallback to the passed service data
          setFormData({
            name: service.name || '',
            description: service.description || '',
            price: service.price || '',
            duration: service.duration || '',
            category: service.category || '',
            isActive: service.isActive !== undefined ? service.isActive : true
          })

          // Load service images from passed data
          if (service.images && service.images.length > 0) {
            setServiceImages(service.images)
          } else if (service.image) {
            setServiceImages([service.image])
          } else {
            setServiceImages([])
          }
        }
      } else if (service) {
        // For new services or when service doesn't have _id, use passed data
        setFormData({
          name: service.name || '',
          description: service.description || '',
          price: service.price || '',
          duration: service.duration || '',
          category: service.category || '',
          isActive: service.isActive !== undefined ? service.isActive : true
        })

        // Load service images
        if (service.images && service.images.length > 0) {
          setServiceImages(service.images)
        } else if (service.image) {
          setServiceImages([service.image])
        } else {
          setServiceImages([])
        }
      } else {
        // Reset form for new service
        setFormData({
          name: '',
          description: '',
          price: '',
          duration: '',
          category: '',
          isActive: true
        })
        setServiceImages([])
      }
      setErrors({})
      setSelectedFiles([])
    }

    fetchServiceData()
  }, [service, isOpen])

  // Load categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoadingCategories(true)
        const data = await categoryService.getServiceCategories()
        setCategories(data)
      } catch (error) {
        console.error('Failed to load categories:', error)
        setCategories([])
      } finally {
        setLoadingCategories(false)
      }
    }

    if (isOpen) {
      loadCategories()
    }
  }, [isOpen])

  // Handle adding new category
  const handleAddNewCategory = async () => {
    if (!newCategoryName.trim()) return

    try {
      const newCategory = await categoryService.createCategory({
        name: newCategoryName.trim(),
        type: 'service',
        description: `Custom service category: ${newCategoryName.trim()}`
      })

      setCategories(prev => [...prev, newCategory])
      setFormData(prev => ({ ...prev, category: newCategory.name }))
      setNewCategoryName('')
      setShowNewCategoryInput(false)
    } catch (error) {
      console.error('Failed to create category:', error)
      // You might want to show a toast notification here
    }
  }

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  // Image upload functions
  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files)
    setSelectedFiles(files)
  }

  const handleImageUpload = async () => {
    if (!service?._id || selectedFiles.length === 0) {
      showError('Please select images to upload')
      return
    }

    setUploadingImage(true)
    try {
      const uploadPromises = selectedFiles.map(file =>
        serviceService.uploadServiceImage(service._id, file)
      )

      const results = await Promise.all(uploadPromises)

      // Update local state with new images
      const newImageUrls = results.map(result => result.data.imageUrl)
      setServiceImages(prev => [...prev, ...newImageUrls])
      setSelectedFiles([])

      showSuccess('Images uploaded successfully!')

      // Reset file input
      const fileInput = document.getElementById('service-image-upload')
      if (fileInput) fileInput.value = ''

    } catch (error) {
      console.error('Image upload error:', error)
      showError('Failed to upload images: ' + (error.response?.data?.message || error.message))
    } finally {
      setUploadingImage(false)
    }
  }

  const handleImageDelete = async (imageUrl) => {
    if (!service?._id) {
      showError('Service ID is required to delete image')
      return
    }

    if (!imageUrl) {
      showError('Image URL is required')
      return
    }

    console.log('Deleting image:', { serviceId: service._id, imageUrl })

    try {
      await serviceService.deleteServiceImage(service._id, imageUrl)
      setServiceImages(prev => prev.filter(img => img !== imageUrl))
      showSuccess('Image deleted successfully!')
    } catch (error) {
      console.error('Image delete error:', error)
      showError('Failed to delete image: ' + (error.response?.data?.message || error.message))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Service name is required'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required'
    }

    if (!formData.price || formData.price <= 0) {
      newErrors.price = 'Valid price is required'
    }

    if (!formData.duration || formData.duration <= 0) {
      newErrors.duration = 'Valid duration is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (viewOnly) return

    if (!validateForm()) return

    setIsLoading(true)
    try {
      await onSave(formData)
    } catch (error) {
      console.error('Error saving service:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <>
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg mr-3">
              <FiSettings className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {viewOnly ? 'Service Details' : service ? 'Edit Service' : 'Add New Service'}
              </h2>
              <p className="text-gray-600">
                {viewOnly ? 'View service information' : service ? 'Update service details' : 'Create a new service offering'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <FiX className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="space-y-6">
            {/* Service Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                disabled={viewOnly}
                className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${
                  errors.name ? 'border-red-300' : 'border-gray-200'
                } ${viewOnly ? 'bg-gray-50' : ''}`}
                placeholder="Enter service name"
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">{errors.name}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                disabled={viewOnly}
                rows={4}
                className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none ${
                  errors.description ? 'border-red-300' : 'border-gray-200'
                } ${viewOnly ? 'bg-gray-50' : ''}`}
                placeholder="Describe the service"
              />
              {errors.description && (
                <p className="text-red-500 text-sm mt-1">{errors.description}</p>
              )}
            </div>

            {/* Price and Duration */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price ($) *
                </label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  disabled={viewOnly}
                  min="0"
                  step="0.01"
                  className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${
                    errors.price ? 'border-red-300' : 'border-gray-200'
                  } ${viewOnly ? 'bg-gray-50' : ''}`}
                  placeholder="0.00"
                />
                {errors.price && (
                  <p className="text-red-500 text-sm mt-1">{errors.price}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Duration (minutes) *
                </label>
                <input
                  type="number"
                  name="duration"
                  value={formData.duration}
                  onChange={handleInputChange}
                  disabled={viewOnly}
                  min="1"
                  className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${
                    errors.duration ? 'border-red-300' : 'border-gray-200'
                  } ${viewOnly ? 'bg-gray-50' : ''}`}
                  placeholder="60"
                />
                {errors.duration && (
                  <p className="text-red-500 text-sm mt-1">{errors.duration}</p>
                )}
              </div>
            </div>

            {/* Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category *
              </label>
              <div className="space-y-2">
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  disabled={viewOnly || loadingCategories}
                  className={`w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${
                    errors.category ? 'border-red-300' : 'border-gray-200'
                  } ${viewOnly || loadingCategories ? 'bg-gray-50 opacity-50' : ''}`}
                >
                  <option value="">
                    {loadingCategories ? 'Loading categories...' : 'Select a category'}
                  </option>
                  {categories.map(category => (
                    <option key={category._id || category.name} value={category.name}>
                      {category.name}
                    </option>
                  ))}
                </select>

                {/* Add new category section */}
                {!viewOnly && !showNewCategoryInput ? (
                  <button
                    type="button"
                    onClick={() => setShowNewCategoryInput(true)}
                    className="flex items-center text-sm text-purple-600 hover:text-purple-700 transition-colors"
                  >
                    <FiPlus className="w-4 h-4 mr-1" />
                    Add new category
                  </button>
                ) : !viewOnly && showNewCategoryInput ? (
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={newCategoryName}
                      onChange={(e) => setNewCategoryName(e.target.value)}
                      placeholder="Enter new category name"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                      onKeyPress={(e) => e.key === 'Enter' && handleAddNewCategory()}
                    />
                    <button
                      type="button"
                      onClick={handleAddNewCategory}
                      disabled={!newCategoryName.trim()}
                      className="px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                    >
                      Add
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setShowNewCategoryInput(false)
                        setNewCategoryName('')
                      }}
                      className="px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm"
                    >
                      Cancel
                    </button>
                  </div>
                ) : null}
              </div>
              {errors.category && (
                <p className="text-red-500 text-sm mt-1">{errors.category}</p>
              )}
            </div>

            {/* Service Images */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service Images
              </label>

              {/* Current Images */}
              {serviceImages.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                  {serviceImages.map((imageUrl, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={imageUrl}
                        alt={`Service image ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg border border-gray-200"
                      />
                      {!viewOnly && (
                        <button
                          type="button"
                          onClick={() => handleImageDelete(imageUrl)}
                          className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"
                        >
                          <FiTrash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Upload New Images - Device Specific */}
              {!viewOnly && service?._id && (
                <div className="space-y-3">
                  <div className="flex items-center gap-3 flex-wrap">
                    {isMobileDevice() ? (
                      // Mobile: Separate camera and gallery access with instant upload
                      <>
                        {deviceImageUpload.createHiddenFileInput()}
                        <button
                          type="button"
                          onClick={deviceImageUpload.triggerMobileCameraAccess}
                          disabled={uploadingImage}
                          className="flex items-center px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <FiCamera className="w-4 h-4 mr-2" />
                          {uploadingImage ? 'Uploading...' : 'Take Photo'}
                        </button>
                        <button
                          type="button"
                          onClick={deviceImageUpload.triggerMobileImageSelection}
                          disabled={uploadingImage}
                          className="flex items-center px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <FiImage className="w-4 h-4 mr-2" />
                          {uploadingImage ? 'Uploading...' : 'Select from Gallery'}
                        </button>
                      </>
                    ) : (
                      // Desktop: Media library modal with instant upload on selection
                      <button
                        type="button"
                        onClick={() => openMediaLibrary({
                          allowUpload: true,
                          multiple: true,
                          onSelect: async (selectedMedia) => {
                            try {
                              setUploadingImage(true)
                              const imageUrls = selectedMedia.map(media => media.url)
                              setServiceImages(prev => [...prev, ...imageUrls])
                              showSuccess(`${selectedMedia.length} image(s) added successfully!`)
                            } catch (error) {
                              showError('Failed to add images')
                            } finally {
                              setUploadingImage(false)
                            }
                          }
                        })}
                        disabled={uploadingImage}
                        className="flex items-center px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <FiImage className="w-4 h-4 mr-2" />
                        {uploadingImage ? 'Processing...' : 'Select Images'}
                      </button>
                    )}

                    {uploadingImage && (
                      <div className="flex items-center text-sm text-gray-500">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
                        Uploading images...
                      </div>
                    )}
                  </div>

                  {isMobileDevice() && (
                    <p className="text-xs text-gray-500">
                      📱 Tap to access camera or photo gallery. Images upload instantly.
                    </p>
                  )}
                </div>
              )}

              {!viewOnly && !service?._id && (
                <p className="text-sm text-gray-500 italic">
                  Save the service first to upload images
                </p>
              )}
            </div>

            {/* Active Status */}
            <div className="flex items-center">
              <input
                type="checkbox"
                name="isActive"
                checked={formData.isActive}
                onChange={handleInputChange}
                disabled={viewOnly}
                className="w-5 h-5 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
              />
              <label className="ml-3 text-sm font-medium text-gray-700">
                Active Service
              </label>
            </div>
          </div>

          {/* Footer */}
          {!viewOnly && (
            <div className="flex justify-end gap-4 mt-8 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-all duration-200"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                ) : (
                  <FiSave className="w-5 h-5 mr-2" />
                )}
                {isLoading ? 'Saving...' : service ? 'Update Service' : 'Create Service'}
              </button>
            </div>
          )}
        </form>
      </div>
    </div>

    {/* Media Library Modal */}
    <MediaLibraryModal
      {...mediaLibraryProps}
      branding={branding}
    />
  </>
  )
}

export default ServiceModal
