@echo off
echo 🚀 MongoDB Migration using mongodump/mongorestore
echo =================================================

echo.
echo 📋 This script will:
echo    1. Export data from source database using mongodump
echo    2. Import data to target database using mongorestore
echo.

REM Source database details
set SOURCE_URI=mongodb+srv://inspiredolajosh:<EMAIL>/MicrolocsHq
set TARGET_URI=mongodb+srv://dammyspicy4:<EMAIL>/MicrolocsHq

REM Create backup directory
set BACKUP_DIR=mongodb-backup
if not exist %BACKUP_DIR% mkdir %BACKUP_DIR%

echo 📦 Step 1: Exporting data from source database...
echo Source: %SOURCE_URI%
echo.

REM Export using mongodump
mongodump --uri="%SOURCE_URI%" --out=%BACKUP_DIR%

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Export failed. Please ensure:
    echo    1. MongoDB tools are installed
    echo    2. Source database is accessible
    echo    3. Network connectivity is working
    pause
    exit /b 1
)

echo.
echo ✅ Export completed successfully!
echo 📁 Data exported to: %BACKUP_DIR%
echo.

echo 📥 Step 2: Importing data to target database...
echo Target: %TARGET_URI%
echo.

REM Import using mongorestore
mongorestore --uri="%TARGET_URI%" --drop %BACKUP_DIR%/MicrolocsHq

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Import failed. Please ensure:
    echo    1. Target database is accessible
    echo    2. You have write permissions
    echo    3. Network connectivity is working
    pause
    exit /b 1
)

echo.
echo 🎉 Migration completed successfully!
echo.
echo 📊 Summary:
echo    ✅ Data exported from source database
echo    ✅ Data imported to target database
echo    📁 Backup files saved in: %BACKUP_DIR%
echo.
echo 🔍 You can verify the migration by checking the target database
pause
